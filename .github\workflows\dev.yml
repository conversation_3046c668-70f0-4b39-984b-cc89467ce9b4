name: Therapist App CI - dev

on:
  push:
    branches: ["pre-development"]

jobs:
  build-and-deploy:
    runs-on: therapist-app-dev-runner

    strategy:
      matrix:
        node-version: [22.x]

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🛠 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: 📦 Install Dependencies
        run: npm install

      - name: 🔑 Setup Environment Variables
        run: |
          touch .env
          echo "${{ secrets.DEV_ENV_FILE }}" > .env

      - name: 🏗️ Build Next.js App
        run: npm run build

      - name: 🚀 Move Build to App Directory
        run: |
          APP_DIR="/home/<USER>/therapist-backoffice-app"

          echo "Moving .next and public folders..."

          # Backup old folders
          rm -rf $APP_DIR/.next_old
          mv $APP_DIR/.next $APP_DIR/.next_old || true

          rm -rf $APP_DIR/public_old
          mv $APP_DIR/public $APP_DIR/public_old || true

          # Move new build
          mv .next $APP_DIR/.next
          mv public $APP_DIR/public

          # Move critical config files
          cp package.json package-lock.json $APP_DIR/

          # Reinstall node_modules
          cd $APP_DIR
          npm ci

          # Restart app using PM2
          pm2 restart 1
          pm2 save
